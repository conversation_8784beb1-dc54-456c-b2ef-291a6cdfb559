import { CollectionOptions, ListCollection } from './list-collection';
export interface UseListCollectionProps<T> extends Omit<CollectionOptions<T>, 'items'> {
    /**
     * The initial items to display in the collection.
     */
    initialItems: T[];
    /**
     * The filter function to use to filter the items.
     */
    filter?: (itemText: string, filterText: string) => boolean;
    /**
     * The maximum number of items to display in the collection.
     * Useful for performance when you have a large number of items.
     */
    limit?: number;
}
export declare function useListCollection<T>(props: UseListCollectionProps<T>): {
    collection: ListCollection<T>;
    filter: (inputValue: string) => void;
    set: (items: T[]) => void;
    reset: () => void;
    clear: () => void;
    insert: (index: number, ...items: T[]) => void;
    insertBefore: (value: string, ...items: T[]) => void;
    insertAfter: (value: string, ...items: T[]) => void;
    remove: (...itemOrValues: T[]) => void;
    move: (value: string, to: number) => void;
    moveBefore: (value: string, ...values: string[]) => void;
    moveAfter: (value: string, ...values: string[]) => void;
    reorder: (from: number, to: number) => void;
    append: (...items: T[]) => void;
    prepend: (...items: T[]) => void;
    update: (value: string, item: T) => void;
};
