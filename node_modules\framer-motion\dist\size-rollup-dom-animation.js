import{c as t,M as e,p as n,q as s,t as i,u as r,v as o,w as a,x as u,y as l,z as h,A as c,B as d,r as p,d as m,o as f,C as y,a as v,D as g,m as b,E as w,b as T,i as A,l as S,f as V,F as M,g as x,s as C,G as k,n as P,h as E,j as F,k as O}from"./size-rollup-dom-animation-assets.js";import{Fragment as D}from"react";const I=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);const N=t=>/^0[^.\s]+$/u.test(t);function R(t){let e;return()=>(void 0===e&&(e=t()),e)}const B=t=>t,K=(t,e)=>n=>e(t(n)),L=(...t)=>t.reduce(K),j=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class U{constructor(){this.subscriptions=[]}add(t){var e,n;return e=this.subscriptions,n=t,-1===e.indexOf(n)&&e.push(n),()=>function(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let i=0;i<s;i++){const s=this.subscriptions[i];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const q=t=>1e3*t,W=t=>t/1e3;function Y(t,e){return e?t*(1e3/e):0}const $=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function z(t,e,n,s){if(t===e&&n===s)return B;const i=e=>function(t,e,n,s,i){let r,o,a=0;do{o=e+(n-e)/2,r=$(o,s,i)-t,r>0?n=o:e=o}while(Math.abs(r)>1e-7&&++a<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:$(i(t),e,s)}const X=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,_=t=>e=>1-t(1-e),H=z(.33,1.53,.69,.99),G=_(H),Z=X(G),J=t=>(t*=2)<1?.5*G(t):.5*(2-Math.pow(2,-10*(t-1))),Q=t=>1-Math.sin(Math.acos(t)),tt=_(Q),et=X(Q),nt=z(.42,0,1,1),st=z(0,0,.58,1),it=z(.42,0,.58,1),rt=t=>Array.isArray(t)&&"number"==typeof t[0],ot={linear:B,easeIn:nt,easeInOut:it,easeOut:st,circIn:Q,circInOut:et,circOut:tt,backIn:G,backInOut:Z,backOut:H,anticipate:J},at=t=>{if(rt(t)){t.length;const[e,n,s,i]=t;return z(e,n,s,i)}return"string"==typeof t?ot[t]:t},{schedule:ut,cancel:lt,state:ht,steps:ct}=t("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:B,!0);let dt;function pt(){dt=void 0}const mt={now:()=>(void 0===dt&&mt.set(ht.isProcessing||e.useManualTiming?ht.timestamp:performance.now()),dt),set:t=>{dt=t,queueMicrotask(pt)}},ft=t=>Math.round(1e5*t)/1e5,yt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const vt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,gt=(t,e)=>n=>Boolean("string"==typeof n&&vt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),bt=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,o,a]=s.match(yt);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},wt={...n,transform:t=>Math.round((t=>i(0,255,t))(t))},Tt={test:gt("rgb","red"),parse:bt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+wt.transform(t)+", "+wt.transform(e)+", "+wt.transform(n)+", "+ft(s.transform(i))+")"};const At={test:gt("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:Tt.transform},St={test:gt("hsl","hue"),parse:bt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+r.transform(ft(e))+", "+r.transform(ft(n))+", "+ft(s.transform(i))+")"},Vt={test:t=>Tt.test(t)||At.test(t)||St.test(t),parse:t=>Tt.test(t)?Tt.parse(t):St.test(t)?St.parse(t):At.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Tt.transform(t):St.transform(t),getAnimatableNone:t=>{const e=Vt.parse(t);return e.alpha=0,Vt.transform(e)}},Mt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const xt="number",Ct="color",kt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Pt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(kt,t=>(Vt.test(t)?(s.color.push(r),i.push(Ct),n.push(Vt.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push(xt),n.push(parseFloat(t))),++r,"${}")).split("${}");return{values:n,split:o,indexes:s,types:i}}function Et(t){return Pt(t).values}function Ft(t){const{split:e,types:n}=Pt(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===xt?ft(t[r]):e===Ct?Vt.transform(t[r]):t[r]}return i}}const Ot=t=>"number"==typeof t?0:Vt.test(t)?Vt.getAnimatableNone(t):t;const Dt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(yt)?.length||0)+(t.match(Mt)?.length||0)>0},parse:Et,createTransformer:Ft,getAnimatableNone:function(t){const e=Et(t);return Ft(t)(e.map(Ot))}};function It(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Nt(t,e){return n=>n>0?e:t}const Rt=(t,e,n)=>t+(e-t)*n,Bt=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Kt=[At,Tt,St];function Lt(t){const e=(n=t,Kt.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===St&&(s=function({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,o=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,a=2*n-s;i=It(a,s,t+1/3),r=It(a,s,t),o=It(a,s,t-1/3)}else i=r=o=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(s)),s}const jt=(t,e)=>{const n=Lt(t),s=Lt(e);if(!n||!s)return Nt(t,e);const i={...n};return t=>(i.red=Bt(n.red,s.red,t),i.green=Bt(n.green,s.green,t),i.blue=Bt(n.blue,s.blue,t),i.alpha=Rt(n.alpha,s.alpha,t),Tt.transform(i))},Ut=new Set(["none","hidden"]);function qt(t,e){return n=>Rt(t,e,n)}function Wt(t){return"number"==typeof t?qt:"string"==typeof t?o(t)?Nt:Vt.test(t)?jt:zt:Array.isArray(t)?Yt:"object"==typeof t?Vt.test(t)?jt:$t:Nt}function Yt(t,e){const n=[...t],s=n.length,i=t.map((t,n)=>Wt(t)(t,e[n]));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function $t(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=Wt(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const zt=(t,e)=>{const n=Dt.createTransformer(e),s=Pt(t),i=Pt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Ut.has(t)&&!i.values.length||Ut.has(e)&&!s.values.length?function(t,e){return Ut.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):L(Yt(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}(s,i),i.values),n):Nt(t,e)};function Xt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Rt(t,e,n);return Wt(t)(t,e)}const _t=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>ut.update(e,t),stop:()=>lt(e),now:()=>ht.isProcessing?ht.timestamp:mt.now()}},Ht=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},Gt=2e4;function Zt(t){let e=0;let n=t.next(e);for(;!n.done&&e<Gt;)e+=50,n=t.next(e);return e>=Gt?1/0:e}function Jt(t,e,n){const s=Math.max(e-5,0);return Y(n-t(s),e-s)}const Qt=100,te=10,ee=1,ne=0,se=800,ie=.3,re=.3,oe={granular:.01,default:2},ae={granular:.005,default:.5},ue=.01,le=10,he=.05,ce=1,de=.001;function pe({duration:t=se,bounce:e=ie,velocity:n=ne,mass:s=ee}){let r,o,a=1-e;a=i(he,ce,a),t=i(ue,le,W(t)),a<1?(r=e=>{const s=e*a,i=s*t,r=s-n,o=fe(e,a),u=Math.exp(-i);return de-r/o*u},o=e=>{const s=e*a*t,i=s*n+n,o=Math.pow(a,2)*Math.pow(e,2)*t,u=Math.exp(-s),l=fe(Math.pow(e,2),a);return(-r(e)+de>0?-1:1)*((i-o)*u)/l}):(r=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const u=function(t,e,n){let s=n;for(let n=1;n<me;n++)s-=t(s)/e(s);return s}(r,o,5/t);if(t=q(t),isNaN(u))return{stiffness:Qt,damping:te,duration:t};{const e=Math.pow(u,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}const me=12;function fe(t,e){return t*Math.sqrt(1-e*e)}const ye=["duration","bounce"],ve=["stiffness","damping","mass"];function ge(t,e){return e.some(e=>void 0!==t[e])}function be(t=re,e=ie){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:r}=n;const o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],u={done:!1,value:o},{stiffness:l,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:ne,stiffness:Qt,damping:te,mass:ee,isResolvedFromDuration:!1,...t};if(!ge(t,ve)&&ge(t,ye))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(1.2*n),r=s*s,o=2*i(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:ee,stiffness:r,damping:o}}else{const n=pe(t);e={...e,...n,mass:ee},e.isResolvedFromDuration=!0}return e}({...n,velocity:-W(n.velocity||0)}),f=p||0,y=h/(2*Math.sqrt(l*c)),v=a-o,g=W(Math.sqrt(l/c)),b=Math.abs(v)<5;let w;if(s||(s=b?oe.granular:oe.default),r||(r=b?ae.granular:ae.default),y<1){const t=fe(g,y);w=e=>{const n=Math.exp(-y*g*e);return a-n*((f+y*g*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}}else if(1===y)w=t=>a-Math.exp(-g*t)*(v+(f+g*v)*t);else{const t=g*Math.sqrt(y*y-1);w=e=>{const n=Math.exp(-y*g*e),s=Math.min(t*e,300);return a-n*((f+y*g*v)*Math.sinh(s)+t*v*Math.cosh(s))/t}}const T={calculatedDuration:m&&d||null,next:t=>{const e=w(t);if(m)u.done=t>=d;else{let n=0===t?f:0;y<1&&(n=0===t?q(f):Jt(w,t,e));const i=Math.abs(n)<=s,o=Math.abs(a-e)<=r;u.done=i&&o}return u.value=u.done?a:e,u},toString:()=>{const t=Math.min(Zt(T),Gt),e=Ht(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function we({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:u,restDelta:l=.5,restSpeed:h}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?u:void 0===u||Math.abs(a-t)<Math.abs(u-t)?a:u;let m=n*e;const f=c+m,y=void 0===o?f:o(f);y!==f&&(m=y-c);const v=t=>-m*Math.exp(-t/s),g=t=>y+v(t),b=t=>{const e=v(t),n=g(t);d.done=Math.abs(e)<=l,d.value=d.done?y:n};let w,T;const A=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==u&&e>u)&&(w=t,T=be({keyframes:[d.value,p(d.value)],velocity:Jt(g,t,d.value),damping:i,stiffness:r,restDelta:l,restSpeed:h}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,b(t),A(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&b(t),d)}}}function Te(t,n,{clamp:s=!0,ease:r,mixer:o}={}){const a=t.length;if(n.length,1===a)return()=>n[0];if(2===a&&n[0]===n[1])return()=>n[1];const u=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),n=[...n].reverse());const l=function(t,n,s){const i=[],r=s||e.mix||Xt,o=t.length-1;for(let e=0;e<o;e++){let s=r(t[e],t[e+1]);if(n){const t=Array.isArray(n)?n[e]||B:n;s=L(t,s)}i.push(s)}return i}(n,r,o),h=l.length,c=e=>{if(u&&e<t[0])return n[0];let s=0;if(h>1)for(;s<t.length-2&&!(e<t[s+1]);s++);const i=j(t[s],t[s+1],e);return l[s](i)};return s?e=>c(i(t[0],t[a-1],e)):c}function Ae(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=j(0,e,s);t.push(Rt(n,1,i))}}(e,t.length-1),e}function Se({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=(t=>Array.isArray(t)&&"number"!=typeof t[0])(s)?s.map(at):at(s),r={done:!1,value:e[0]},o=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Ae(e),t),a=Te(o,e,{ease:Array.isArray(i)?i:(u=e,l=i,u.map(()=>l||it).splice(0,u.length-1))});var u,l;return{calculatedDuration:t,next:e=>(r.value=a(e),r.done=e>=t,r)}}be.applyToOptions=t=>{const e=function(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Zt(s),Gt);return{type:"keyframes",ease:t=>s.next(i*t).value/e,duration:W(i)}}(t,100,be);return t.ease=e.ease,t.duration=q(e.duration),t.type="keyframes",t};const Ve=t=>null!==t;function Me(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(Ve),o=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return o&&void 0!==s?s:r[o]}const xe={decay:we,inertia:we,tween:Se,keyframes:Se,spring:be};function Ce(t){"string"==typeof t.type&&(t.type=xe[t.type])}class ke{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Pe=t=>t/100;class Ee extends ke{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==mt.now()&&this.tick(mt.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ce(t);const{type:e=Se,repeat:n=0,repeatDelay:s=0,repeatType:i,velocity:r=0}=t;let{keyframes:o}=t;const a=e||Se;a!==Se&&"number"!=typeof o[0]&&(this.mixKeyframes=L(Pe,Xt(o[0],o[1])),o=[0,100]);const u=a({...t,keyframes:o});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===u.calculatedDuration&&(u.calculatedDuration=Zt(u));const{calculatedDuration:l}=u;this.calculatedDuration=l,this.resolvedDuration=l+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=u}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:s,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:u}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:h,repeat:c,repeatType:d,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const v=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?v<0:v>s;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let b=this.currentTime,w=n;if(c){const t=Math.min(this.currentTime,s)/a;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===d?(n=1-n,p&&(n-=p/a)):"mirror"===d&&(w=o)),b=i(0,1,n)*a}const T=g?{done:!1,value:h[0]}:w.next(b);r&&(T.value=r(T.value));let{done:A}=T;g||null===u||(A=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&A);return S&&m!==we&&(T.value=Me(h,this.options,y,this.speed)),f&&f(T.value),S&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return W(this.calculatedDuration)}get time(){return W(this.currentTime)}set time(t){t=q(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(mt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=W(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=_t,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(mt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const Fe=t=>180*t/Math.PI,Oe=t=>{const e=Fe(Math.atan2(t[1],t[0]));return Ie(e)},De={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Oe,rotateZ:Oe,skewX:t=>Fe(Math.atan(t[1])),skewY:t=>Fe(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ie=t=>((t%=360)<0&&(t+=360),t),Ne=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Re=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Be={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ne,scaleY:Re,scale:t=>(Ne(t)+Re(t))/2,rotateX:t=>Ie(Fe(Math.atan2(t[6],t[5]))),rotateY:t=>Ie(Fe(Math.atan2(-t[2],t[0]))),rotateZ:Oe,rotate:Oe,skewX:t=>Fe(Math.atan(t[4])),skewY:t=>Fe(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ke(t){return t.includes("scale")?1:0}function Le(t,e){if(!t||"none"===t)return Ke(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Be,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=De,i=e}if(!i)return Ke(e);const r=s[e],o=i[1].split(",").map(je);return"function"==typeof r?r(o):o[r]}function je(t){return parseFloat(t.trim())}const Ue=t=>t===n||t===a,qe=new Set(["x","y","z"]),We=u.filter(t=>!qe.has(t));const Ye={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Le(e,"x"),y:(t,{transform:e})=>Le(e,"y")};Ye.translateX=Ye.x,Ye.translateY=Ye.y;const $e=new Set;let ze=!1,Xe=!1,_e=!1;function He(){if(Xe){const t=Array.from($e).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return We.forEach(n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Xe=!1,ze=!1,$e.forEach(t=>t.complete(_e)),$e.clear()}function Ge(){$e.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Xe=!0)})}class Ze{constructor(t,e,n,s,i,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?($e.add(this),ze||(ze=!0,ut.read(Ge),ut.resolveKeyframes(He))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),$e.delete(this)}cancel(){"scheduled"===this.state&&($e.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const Je=R(()=>void 0!==window.ScrollTimeline),Qe={};function tn(t,e){const n=R(t);return()=>Qe[e]??n()}const en=tn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),nn=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,sn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:nn([0,.65,.55,1]),circOut:nn([.55,0,1,.45]),backIn:nn([.31,.01,.66,-.59]),backOut:nn([.33,1.53,.69,.99])};function rn(t,e){return t?"function"==typeof t?en()?Ht(t,e):"ease-out":rt(t)?nn(t):Array.isArray(t)?t.map(t=>rn(t,e)||sn.easeOut):sn[t]:void 0}function on(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:u}={},l=void 0){const h={[e]:n};u&&(h.offset=u);const c=rn(a,i);Array.isArray(c)&&(h.easing=c);const d={delay:s,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};l&&(d.pseudoElement=l);return t.animate(h,d)}function an(t){return"function"==typeof t&&"applyToOptions"in t}class un extends ke{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:i,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(i),this.allowFlatten=r,this.options=t,t.type;const u=function({type:t,...e}){return an(t)&&en()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=on(e,n,s,u,i),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const t=Me(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return W(Number(t))}get time(){return W(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=q(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Je()?(this.animation.timeline=t,B):e(this)}}const ln={anticipate:J,backInOut:Z,circInOut:et};function hn(t){"string"==typeof t.ease&&t.ease in ln&&(t.ease=ln[t.ease])}class cn extends un{constructor(t){hn(t),Ce(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:i,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new Ee({...r,autoplay:!1}),a=q(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const dn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Dt.test(t)&&"0"!==t||t.startsWith("url(")));function pn(t){return"object"==typeof(e=t)&&null!==e&&"offsetHeight"in t;var e}const mn=new Set(["opacity","clipPath","filter","transform"]),fn=R(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class yn extends ke{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:o,name:a,motionValue:u,element:l,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=mt.now();const c={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:a,motionValue:u,element:l,...h},d=l?.KeyframeResolver||Ze;this.keyframeResolver=new d(o,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),a,u,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,s,i){this.keyframeResolver=void 0;const{name:r,type:o,velocity:a,delay:u,isHandoff:l,onUpdate:h}=s;this.resolvedAt=mt.now(),function(t,e,n,s){const i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],o=dn(i,e),a=dn(r,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||an(n))&&s)}(t,r,o,a)||(!e.instantAnimations&&u||h?.(Me(t,s,n)),t[0]=t[t.length-1],s.duration=0,s.repeat=0);const c={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:t},d=!l&&function(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:o}=t;if(!pn(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:u}=e.owner.getProps();return fn()&&n&&mn.has(n)&&("transform"!==n||!u)&&!a&&!s&&"mirror"!==i&&0!==r&&"inertia"!==o}(c)?new cn({...c,element:c.motionValue.owner.current}):new Ee(c);d.finished.then(()=>this.notifyFinished()).catch(B),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),_e=!0,Ge(),He(),_e=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const vn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function gn(t,e,n=1){const[s,i]=function(t){const e=vn.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const t=r.trim();return I(t)?parseFloat(t):t}return o(i)?gn(i,e,n+1):i}function bn(t,e){return t?.[e]??t?.default??t}const wn=new Set(["width","height","top","left","right","bottom",...u]),Tn=t=>e=>e.test(t),An=[n,a,r,l,h,c,{test:t=>"auto"===t,parse:t=>t}],Sn=t=>An.find(Tn(t));function Vn(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||N(t))}const Mn=new Set(["brightness","contrast","saturate","opacity"]);function xn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(yt)||[];if(!s)return t;const i=n.replace(s,"");let r=Mn.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Cn=/\b([a-z-]*)\(.*?\)/gu,kn={...Dt,getAnimatableNone:t=>{const e=t.match(Cn);return e?e.map(xn).join(" "):t}},Pn={...d,color:Vt,backgroundColor:Vt,outlineColor:Vt,fill:Vt,stroke:Vt,borderColor:Vt,borderTopColor:Vt,borderRightColor:Vt,borderBottomColor:Vt,borderLeftColor:Vt,filter:kn,WebkitFilter:kn},En=t=>Pn[t];function Fn(t,e){let n=En(t);return n!==kn&&(n=Dt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const On=new Set(["auto","none","0"]);class Dn extends Ze{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),o(s))){const i=gn(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!wn.has(n)||2!==t.length)return;const[s,i]=t,r=Sn(s),a=Sn(i);if(r!==a)if(Ue(r)&&Ue(a))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else Ye[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||Vn(t[e]))&&n.push(e);n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!On.has(e)&&Pt(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=Fn(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ye[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=Ye[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}class In{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=mt.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=mt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new U);const n=this.events[t].add(e);return"change"===t?()=>{n(),ut.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=mt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Y(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Nn(t,e){return new In(t,e)}const Rn=!1;function Bn(){return Rn}function Kn(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function Ln(t){return!("touch"===t.pointerType||Bn())}const jn=(t,e)=>!!e&&(t===e||jn(t,e.parentElement)),Un=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const qn=new WeakSet;function Wn(t){return e=>{"Enter"===e.key&&t(e)}}function Yn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function $n(t){return(t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary)(t)&&!Bn()}function zn(t,e,n={}){const[s,i,r]=Kn(t,n),o=t=>{const s=t.currentTarget;if(!$n(t))return;qn.add(s);const r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",u),qn.has(s)&&qn.delete(s),$n(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,s===window||s===document||n.useGlobalTarget||jn(s,t.target))},u=t=>{o(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",u,i)};return s.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",o,i),pn(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=Wn(()=>{if(qn.has(n))return;Yn(n,"down");const t=Wn(()=>{Yn(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>Yn(n,"cancel"),e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)})(t,i)),e=t,Un.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}const Xn=[...An,Vt,Dt];function _n(t,e,n){const s=t.getProps();return p(s,e,void 0!==n?n:s.custom,t)}const Hn=t=>Array.isArray(t);function Gn(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Nn(n))}function Zn(t){return Hn(t)?t[t.length-1]||0:t}function Jn(t,n){const s=t.getValue("willChange");if(i=s,Boolean(m(i)&&i.add))return s.add(n);if(!s&&e.WillChange){const s=new e.WillChange("auto");t.addValue("willChange",s),s.add(n)}var i}function Qn(t){return t.props[f]}const ts=t=>null!==t;const es={type:"spring",stiffness:500,damping:25,restSpeed:10},ns={type:"keyframes",duration:.8},ss={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},is=(t,{keyframes:e})=>e.length>2?ns:y.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:es:ss;const rs=(t,n,s,i={},r,o)=>a=>{const u=bn(i,t)||{},l=u.delay||i.delay||0;let{elapsed:h=0}=i;h-=q(l);const c={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:n.getVelocity(),...u,delay:-h,onUpdate:t=>{n.set(t),u.onUpdate&&u.onUpdate(t)},onComplete:()=>{a(),u.onComplete&&u.onComplete()},name:t,motionValue:n,element:o?void 0:r};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:u,elapsed:l,...h}){return!!Object.keys(h).length})(u)||Object.assign(c,is(t,c)),c.duration&&(c.duration=q(c.duration)),c.repeatDelay&&(c.repeatDelay=q(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(d=!0)),(e.instantAnimations||e.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),c.allowFlatten=!u.type&&!u.ease,d&&!o&&void 0!==n.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(ts),r=e&&"loop"!==n&&e%2==1?0:i.length-1;return r&&void 0!==s?s:i[r]}(c.keyframes,u);if(void 0!==t)return void ut.update(()=>{c.onUpdate(t),c.onComplete()})}return u.isSync?new Ee(c):new yn(c)};function os({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function as(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const u=[],l=i&&t.animationState&&t.animationState.getState()[i];for(const e in a){const s=t.getValue(e,t.latestValues[e]??null),i=a[e];if(void 0===i||l&&os(l,e))continue;const o={delay:n,...bn(r||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(i)&&i===h&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const n=Qn(t);if(n){const t=window.MotionHandoffAnimation(n,e,ut);null!==t&&(o.startTime=t,c=!0)}}Jn(t,e),s.start(rs(e,s,i,t.shouldReduceMotion&&wn.has(e)?{type:!1}:o,t,c));const d=s.animation;d&&u.push(d)}return o&&Promise.all(u).then(()=>{ut.update(()=>{o&&function(t,e){const n=_n(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const e in r)Gn(t,e,Zn(r[e]))}(t,o)})}),u}function us(t,e,n={}){const s=_n(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(as(t,s,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{const{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=i;return function(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,u=1===i?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(ls).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(us(t,e,{...r,delay:n+u(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+s,o,a,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[t,e]="beforeChildren"===a?[r,o]:[o,r];return t().then(()=>e())}return Promise.all([r(),o(n.delay)])}function ls(t,e){return t.sortNodePosition(e)}function hs(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const cs=g.length;function ds(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&ds(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<cs;n++){const s=g[n],i=t.props[s];(v(i)||!1===i)&&(e[s]=i)}return e}const ps=[...w].reverse(),ms=w.length;function fs(t){return e=>Promise.all(e.map(({animation:e,options:n})=>function(t,e,n={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e)){const i=e.map(e=>us(t,e,n));s=Promise.all(i)}else if("string"==typeof e)s=us(t,e,n);else{const i="function"==typeof e?_n(t,e,n.custom):e;s=Promise.all(as(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}(t,e,n)))}function ys(t){let e=fs(t),n=bs(),s=!0;const i=e=>(n,s)=>{const i=_n(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(i){const{transition:t,transitionEnd:e,...s}=i;n={...n,...s,...e}}return n};function r(r){const{props:o}=t,a=ds(t.parent)||{},u=[],l=new Set;let h={},c=1/0;for(let e=0;e<ms;e++){const d=ps[e],p=n[d],m=void 0!==o[d]?o[d]:a[d],f=v(m),y=d===r?p.isActive:null;!1===y&&(c=e);let g=m===a[d]&&m!==o[d]&&f;if(g&&s&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...h},!p.isActive&&null===y||!m&&!p.prevProp||b(m)||"boolean"==typeof m)continue;const w=vs(p.prevProp,m);let T=w||d===r&&p.isActive&&!g&&f||e>c&&f,A=!1;const S=Array.isArray(m)?m:[m];let V=S.reduce(i(d),{});!1===y&&(V={});const{prevResolvedValues:M={}}=p,x={...M,...V},C=e=>{T=!0,l.has(e)&&(A=!0,l.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in x){const e=V[t],n=M[t];if(h.hasOwnProperty(t))continue;let s=!1;s=Hn(e)&&Hn(n)?!hs(e,n):e!==n,s?null!=e?C(t):l.add(t):void 0!==e&&l.has(t)?C(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=V,p.isActive&&(h={...h,...V}),s&&t.blockInitialAnimation&&(T=!1);T&&(!(g&&w)||A)&&u.push(...S.map(t=>({animation:t,options:{type:d}})))}if(l.size){const e={};if("boolean"!=typeof o.initial){const n=_n(t,Array.isArray(o.initial)?o.initial[0]:o.initial);n&&n.transition&&(e.transition=n.transition)}l.forEach(n=>{const s=t.getBaseTarget(n),i=t.getValue(n);i&&(i.liveStyle=!0),e[n]=s??null}),u.push({animation:e})}let d=Boolean(u.length);return!s||!1!==o.initial&&o.initial!==o.animate||t.manuallyAnimateOnMount||(d=!1),s=!1,d?e(u):Promise.resolve()}return{animateChanges:r,setActive:function(e,s){if(n[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),n[e].isActive=s;const i=r(e);for(const t in n)n[t].protectedKeys={};return i},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=bs(),s=!0}}}function vs(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!hs(e,t)}function gs(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function bs(){return{animate:gs(!0),whileInView:gs(),whileHover:gs(),whileTap:gs(),whileDrag:gs(),whileFocus:gs(),exit:gs()}}class ws{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ts=0;const As={animation:{Feature:class extends ws{constructor(t){super(t),t.animationState||(t.animationState=ys(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();b(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends ws{constructor(){super(...arguments),this.id=Ts++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Ss(t){return{point:{x:t.pageX,y:t.pageY}}}function Vs(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===n);const i=s["onHover"+n];i&&ut.postRender(()=>i(e,Ss(e)))}function Ms(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function xs(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===n);const i=s["onTap"+("End"===n?"":n)];i&&ut.postRender(()=>i(e,Ss(e)))}const Cs=new WeakMap,ks=new WeakMap,Ps=t=>{const e=Cs.get(t.target);e&&e(t)},Es=t=>{t.forEach(Ps)};function Fs(t,e,n){const s=function({root:t,...e}){const n=t||document;ks.has(n)||ks.set(n,{});const s=ks.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Es,{root:t,...e})),s[i]}(e);return Cs.set(t,n),s.observe(t),()=>{Cs.delete(t),s.unobserve(t)}}const Os={some:0,all:1};const Ds={inView:{Feature:class extends ws{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:s="some",once:i}=t,r={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof s?s:Os[s]};return Fs(this.node.current,r,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,i&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:s}=this.node.getProps(),r=e?n:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends ws{mount(){const{current:t}=this.node;t&&(this.unmount=zn(t,(t,e)=>(xs(this.node,e,"Start"),(t,{success:e})=>xs(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends ws{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(Ms(this.node.current,"focus",()=>this.onFocus()),Ms(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends ws{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[s,i,r]=Kn(t,n),o=t=>{if(!Ln(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const r=t=>{Ln(t)&&(s(t),n.removeEventListener("pointerleave",r))};n.addEventListener("pointerleave",r,i)};return s.forEach(t=>{t.addEventListener("pointerenter",o,i)}),r}(t,(t,e)=>(Vs(this.node,e,"Start"),t=>Vs(this.node,t,"End"))))}unmount(){}}}};const Is=()=>({x:{min:0,max:0},y:{min:0,max:0}}),Ns={current:null},Rs={current:!1};const Bs=new WeakMap;const Ks=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ls{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:i,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ze,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=mt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,ut.render(this.render,!1,!0))};const{latestValues:a,renderState:u}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=A(e),this.isVariantNode=S(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:l,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in h){const e=h[t];void 0!==a[t]&&m(e)&&e.set(a[t],!1)}}mount(t){this.current=t,Bs.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Rs.current||function(){if(Rs.current=!0,T)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ns.current=t.matches;t.addListener(e),e()}else Ns.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ns.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),lt(this.notifyUpdate),lt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=y.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&ut.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),i=e.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in V){const e=V[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Ks.length;e++){const n=Ks[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const i=e[s],r=n[s];if(m(i))t.addValue(s,i);else if(m(r))t.addValue(s,Nn(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{const e=t.getStaticValue(s);t.addValue(s,Nn(void 0!==e?e:i,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Nn(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=n&&("string"==typeof n&&(I(n)||N(n))?n=parseFloat(n):(s=n,!Xn.find(Tn(s))&&Dt.test(e)&&(n=Fn(t,e))),this.setBaseTarget(t,m(n)?n.get():n)),m(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=p(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||m(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new U),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class js extends Ls{constructor(){super(...arguments),this.KeyframeResolver=Dn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;m(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function Us(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const e in n)t.style.setProperty(e,n[e])}class qs extends js{constructor(){super(...arguments),this.type="html",this.renderInstance=Us}readValueFromInstance(t,e){if(y.has(e))return this.projection?.isProjecting?Ke(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Le(n,e)})(t,e);{const s=(n=t,window.getComputedStyle(n)),i=(M(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){x(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return C(t,e,n)}}const Ws=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class Ys extends js{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Is}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(y.has(e)){const t=En(e);return t&&t.default||0}return e=Ws.has(e)?e:k(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return P(t,e,n)}build(t,e,n){E(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){!function(t,e,n,s){Us(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(Ws.has(n)?n:k(n),e.attrs[n])}(t,e,0,s)}mount(t){this.isSVGTag=F(t.tagName),super.mount(t)}}const $s={renderer:(t,e)=>O(t)?new Ys(e):new qs(e,{allowProjection:t!==D}),...As,...Ds};export{$s as domAnimation};
